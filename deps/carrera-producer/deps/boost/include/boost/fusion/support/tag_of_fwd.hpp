/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>
    Copyright (c) 2005-2006 <PERSON>

    Di<PERSON>ributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_FUSION_TAG_OF_FWD_31122005_1445)
#define BOOST_FUSION_TAG_OF_FWD_31122005_1445

namespace boost { namespace fusion
{
    namespace traits
    {
        template<typename T, typename Active = void>
        struct tag_of;
    }
}}

#endif
